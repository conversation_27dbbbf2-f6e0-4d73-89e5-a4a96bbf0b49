const express = require('express');
const database = require('../database/database');

const router = express.Router();

// 获取帖子列表
router.get('/', async (req, res) => {
    try {
        const posts = await database.getPosts();
        res.json({
            success: true,
            data: posts,
            message: '获取帖子列表成功'
        });
    } catch (error) {
        console.error('获取帖子列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取帖子列表失败',
            error: error.message
        });
    }
});

// 根据ID获取帖子详情
router.get('/:id', async (req, res) => {
    try {
        const postId = req.params.id;
        
        // 验证ID是否为数字
        if (!/^\d+$/.test(postId)) {
            return res.status(400).json({
                success: false,
                message: '无效的帖子ID'
            });
        }

        const post = await database.getPostById(postId);
        
        if (!post) {
            return res.status(404).json({
                success: false,
                message: '帖子不存在'
            });
        }

        res.json({
            success: true,
            data: post,
            message: '获取帖子详情成功'
        });
    } catch (error) {
        console.error('获取帖子详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取帖子详情失败',
            error: error.message
        });
    }
});

// 创建新帖子
router.post('/', async (req, res) => {
    try {
        const { title, content, author } = req.body;

        // 验证必填字段
        if (!title || !content || !author) {
            return res.status(400).json({
                success: false,
                message: '标题、内容和作者都是必填字段'
            });
        }

        // 验证字段长度
        if (title.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '标题不能为空'
            });
        }

        if (content.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '内容不能为空'
            });
        }

        if (author.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '作者不能为空'
            });
        }

        const result = await database.createPost(title.trim(), content.trim(), author.trim());
        
        res.status(201).json({
            success: true,
            data: {
                id: result.id,
                title: title.trim(),
                content: content.trim(),
                author: author.trim()
            },
            message: '帖子创建成功'
        });
    } catch (error) {
        console.error('创建帖子失败:', error);
        res.status(500).json({
            success: false,
            message: '创建帖子失败',
            error: error.message
        });
    }
});

// 获取帖子的所有回复
router.get('/:id/replies', async (req, res) => {
    try {
        const postId = req.params.id;
        
        // 验证ID是否为数字
        if (!/^\d+$/.test(postId)) {
            return res.status(400).json({
                success: false,
                message: '无效的帖子ID'
            });
        }

        // 先检查帖子是否存在
        const post = await database.getPostById(postId);
        if (!post) {
            return res.status(404).json({
                success: false,
                message: '帖子不存在'
            });
        }

        const replies = await database.getRepliesByPostId(postId);
        
        res.json({
            success: true,
            data: replies,
            message: '获取回复列表成功'
        });
    } catch (error) {
        console.error('获取回复列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取回复列表失败',
            error: error.message
        });
    }
});

// 创建回复
router.post('/:id/replies', async (req, res) => {
    try {
        const postId = req.params.id;
        const { content, author } = req.body;

        // 验证ID是否为数字
        if (!/^\d+$/.test(postId)) {
            return res.status(400).json({
                success: false,
                message: '无效的帖子ID'
            });
        }

        // 验证必填字段
        if (!content || !author) {
            return res.status(400).json({
                success: false,
                message: '回复内容和作者都是必填字段'
            });
        }

        // 验证字段长度
        if (content.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '回复内容不能为空'
            });
        }

        if (author.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: '作者不能为空'
            });
        }

        // 先检查帖子是否存在
        const post = await database.getPostById(postId);
        if (!post) {
            return res.status(404).json({
                success: false,
                message: '帖子不存在'
            });
        }

        const result = await database.createReply(postId, content.trim(), author.trim());
        
        res.status(201).json({
            success: true,
            data: {
                id: result.id,
                post_id: postId,
                content: content.trim(),
                author: author.trim()
            },
            message: '回复创建成功'
        });
    } catch (error) {
        console.error('创建回复失败:', error);
        res.status(500).json({
            success: false,
            message: '创建回复失败',
            error: error.message
        });
    }
});

module.exports = router;
