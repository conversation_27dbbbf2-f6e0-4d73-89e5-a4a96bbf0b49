/* V2EX 风格全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Helvetica Neue", "Luxi Sans", "Segoe UI", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f2f2f2;
    min-width: 820px;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
    background-color: #fff;
    min-height: 100vh;
}

/* V2EX 风格头部样式 */
.header {
    background: #fff;
    border-bottom: 1px solid #e2e2e2;
    padding: 15px 0;
    margin-bottom: 0;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 44px;
}

.header h1 {
    font-size: 18px;
    font-weight: normal;
    color: #333;
    margin: 0;
}

.header p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

/* V2EX 风格导航样式 */
.nav {
    background: #fff;
    border-bottom: 1px solid #e2e2e2;
    padding: 10px 0;
    margin-bottom: 20px;
}

.nav a {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 5px;
    color: #778087;
    text-decoration: none;
    font-size: 14px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.nav a:hover {
    background: #f5f5f5;
    color: #333;
}

.nav a.active {
    background: #778087;
    color: #fff;
}

/* V2EX 风格卡片样式 */
.card {
    background: #fff;
    border: 1px solid #e2e2e2;
    border-radius: 3px;
    margin-bottom: 15px;
    overflow: hidden;
}

.card-header {
    background: #f8f8f8;
    border-bottom: 1px solid #e2e2e2;
    padding: 10px 15px;
    font-size: 14px;
    color: #666;
}

.card-body {
    padding: 15px;
}

/* V2EX 风格帖子列表样式 */
.post-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    transition: background-color 0.2s ease;
}

.post-item:hover {
    background: #f8f8f8;
}

.post-item:last-child {
    border-bottom: none;
}

.post-avatar {
    width: 48px;
    height: 48px;
    border-radius: 3px;
    margin-right: 15px;
    background: #e2e2e2;
    flex-shrink: 0;
}

.post-content {
    flex: 1;
    min-width: 0;
}

.post-title {
    font-size: 16px;
    font-weight: normal;
    color: #333;
    text-decoration: none;
    display: block;
    margin-bottom: 8px;
    line-height: 1.3;
}

.post-title:hover {
    color: #778087;
}

.post-meta {
    color: #ccc;
    font-size: 12px;
    margin-bottom: 8px;
}

.post-meta a {
    color: #778087;
    text-decoration: none;
}

.post-meta a:hover {
    color: #333;
}

.post-content-preview {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.post-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.post-replies {
    background: #e2e2e2;
    color: #999;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    min-width: 20px;
    text-align: center;
}

.post-time {
    color: #ccc;
    font-size: 12px;
}

/* V2EX 风格表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
    font-weight: normal;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.2s ease;
    background: #fff;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #778087;
    box-shadow: none;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.5;
}

/* V2EX 风格按钮样式 */
.btn {
    display: inline-block;
    padding: 6px 12px;
    background: #778087;
    color: #fff;
    text-decoration: none;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    font-family: inherit;
    transition: all 0.2s ease;
    line-height: 1.4;
}

.btn:hover {
    background: #5a6268;
    color: #fff;
}

.btn-primary {
    background: #778087;
}

.btn-primary:hover {
    background: #5a6268;
}

.btn-success {
    background: #52c41a;
}

.btn-success:hover {
    background: #389e0d;
}

.btn-outline {
    background: transparent;
    color: #778087;
    border: 1px solid #ccc;
}

.btn-outline:hover {
    background: #f5f5f5;
    color: #333;
    border-color: #999;
}

/* V2EX 风格帖子详情样式 */
.post-detail {
    background: #fff;
    border: 1px solid #e2e2e2;
    border-radius: 3px;
    margin-bottom: 15px;
    overflow: hidden;
}

.post-detail-header {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.post-detail h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: normal;
    line-height: 1.3;
}

.post-detail .post-meta {
    color: #ccc;
    font-size: 12px;
    margin-bottom: 0;
}

.post-detail .post-meta a {
    color: #778087;
    text-decoration: none;
}

.post-detail .post-meta a:hover {
    color: #333;
}

.post-detail-body {
    padding: 15px;
}

.post-detail .post-content {
    line-height: 1.6;
    color: #333;
    font-size: 14px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* V2EX 风格回复样式 */
.replies-section {
    margin-top: 15px;
}

.replies-section h3 {
    background: #f8f8f8;
    color: #666;
    font-size: 14px;
    font-weight: normal;
    margin: 0;
    padding: 10px 15px;
    border-bottom: 1px solid #e2e2e2;
}

.reply-item {
    background: #fff;
    border: 1px solid #e2e2e2;
    border-top: none;
    padding: 15px;
    display: flex;
}

.reply-item:last-child {
    border-bottom: 1px solid #e2e2e2;
}

.reply-avatar {
    width: 32px;
    height: 32px;
    border-radius: 3px;
    margin-right: 12px;
    background: #e2e2e2;
    flex-shrink: 0;
}

.reply-content-wrapper {
    flex: 1;
    min-width: 0;
}

.reply-meta {
    color: #ccc;
    font-size: 12px;
    margin-bottom: 8px;
}

.reply-meta a {
    color: #778087;
    text-decoration: none;
}

.reply-meta a:hover {
    color: #333;
}

.reply-content {
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* V2EX 风格回复表单样式 */
.reply-form {
    background: #fff;
    border: 1px solid #e2e2e2;
    border-radius: 3px;
    margin-top: 15px;
    overflow: hidden;
}

.reply-form h4 {
    background: #f8f8f8;
    color: #666;
    font-size: 14px;
    font-weight: normal;
    margin: 0;
    padding: 10px 15px;
    border-bottom: 1px solid #e2e2e2;
}

.reply-form-body {
    padding: 15px;
}

/* V2EX 风格消息提示样式 */
.message {
    padding: 12px 15px;
    margin-bottom: 15px;
    border-radius: 3px;
    font-size: 14px;
}

.message.success {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.message.error {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 30px;
    color: #999;
    font-size: 14px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 30px;
    color: #999;
    font-size: 14px;
}

.empty-state h3 {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: normal;
}

/* 页脚样式 */
footer {
    background: #fff;
    border-top: 1px solid #e2e2e2;
    padding: 20px 0;
    margin-top: 30px;
    text-align: center;
    color: #999;
    font-size: 12px;
}

footer a {
    color: #778087;
    text-decoration: none;
}

footer a:hover {
    color: #333;
}

/* V2EX 风格响应式设计 */
@media (max-width: 820px) {
    body {
        min-width: auto;
    }

    .container {
        padding: 0 10px;
    }

    .header .container {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .nav {
        padding: 8px 0;
    }

    .nav a {
        padding: 6px 10px;
        margin: 0 3px;
        font-size: 13px;
    }

    .post-item {
        flex-direction: column;
        padding: 12px;
    }

    .post-avatar {
        width: 32px;
        height: 32px;
        margin-right: 0;
        margin-bottom: 10px;
        align-self: flex-start;
    }

    .post-title {
        font-size: 15px;
    }

    .post-content-preview {
        font-size: 13px;
    }

    .reply-item {
        flex-direction: column;
        padding: 12px;
    }

    .reply-avatar {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .btn {
        padding: 8px 12px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 5px;
    }

    .post-item {
        padding: 10px;
    }

    .card-body {
        padding: 12px;
    }

    .reply-form-body {
        padding: 12px;
    }
}
