/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    padding: 20px 0;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    text-align: center;
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header p {
    text-align: center;
    color: #7f8c8d;
    font-size: 1.1em;
}

/* 导航样式 */
.nav {
    text-align: center;
    margin: 20px 0;
}

.nav a {
    display: inline-block;
    padding: 10px 20px;
    margin: 0 10px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav a:hover {
    background: #2980b9;
}

.nav a.active {
    background: #2c3e50;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 帖子列表样式 */
.post-item {
    border-bottom: 1px solid #eee;
    padding: 15px 0;
}

.post-item:last-child {
    border-bottom: none;
}

.post-title {
    font-size: 1.3em;
    font-weight: bold;
    color: #2c3e50;
    text-decoration: none;
    display: block;
    margin-bottom: 8px;
}

.post-title:hover {
    color: #3498db;
}

.post-meta {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.post-content-preview {
    color: #555;
    line-height: 1.5;
    margin-bottom: 10px;
}

.post-stats {
    color: #95a5a6;
    font-size: 0.85em;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn:hover {
    background: #2980b9;
}

.btn-primary {
    background: #3498db;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
}

.btn-success:hover {
    background: #229954;
}

/* 帖子详情样式 */
.post-detail {
    margin-bottom: 30px;
}

.post-detail h1 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 2em;
}

.post-detail .post-meta {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.post-detail .post-content {
    line-height: 1.8;
    color: #444;
    margin-bottom: 30px;
    white-space: pre-wrap;
}

/* 回复样式 */
.replies-section {
    margin-top: 40px;
}

.replies-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.reply-item {
    background: #f8f9fa;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.reply-meta {
    color: #7f8c8d;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.reply-content {
    color: #555;
    line-height: 1.6;
    white-space: pre-wrap;
}

/* 回复表单样式 */
.reply-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-top: 30px;
}

.reply-form h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

/* 消息提示样式 */
.message {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

.empty-state h3 {
    margin-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .nav a {
        display: block;
        margin: 5px 0;
    }
    
    .post-title {
        font-size: 1.2em;
    }
    
    .post-detail h1 {
        font-size: 1.5em;
    }
}
