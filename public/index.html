<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HaoWriter 极简论坛</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>HaoWriter 论坛</h1>
            <p>一个极简而优雅的交流平台</p>
        </div>
    </div>

    <div class="nav">
        <div class="container">
            <a href="/" class="active">首页</a>
            <a href="/list">帖子列表</a>
            <a href="/post">发表帖子</a>
        </div>
    </div>

    <div class="container">

        <div class="card">
            <div class="card-header">
                欢迎来到 HaoWriter 论坛
            </div>
            <div class="card-body">
                <p>这是一个基于 Express.js 和 SQLite 构建的极简论坛系统。在这里，您可以：</p>

                <div style="margin: 15px 0;">
                    <h4>主要功能</h4>
                    <ul style="margin-left: 20px; line-height: 1.6;">
                        <li>发表帖子 - 分享您的想法和见解</li>
                        <li>参与讨论 - 回复感兴趣的帖子</li>
                        <li>浏览内容 - 查看所有用户发布的帖子</li>
                        <li>简洁界面 - 专注于内容，无干扰体验</li>
                    </ul>
                </div>

                <div style="margin: 15px 0;">
                    <h4>快速开始</h4>
                    <p>选择下面的操作开始您的论坛之旅：</p>

                    <div style="margin-top: 15px;">
                        <a href="/list" class="btn btn-primary" style="margin-right: 10px;">浏览帖子</a>
                        <a href="/post" class="btn btn-success">发表帖子</a>
                    </div>
                </div>

                <div style="margin: 15px 0;">
                    <h4>使用提示</h4>
                    <ul style="margin-left: 20px; line-height: 1.6; color: #666;">
                        <li>点击帖子标题可以查看详细内容和回复</li>
                        <li>发帖时请填写真实的作者姓名</li>
                        <li>回复时请保持友善和建设性的讨论</li>
                        <li>系统会自动记录发帖和回复的时间</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                技术特性
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div>
                        <h4 style="color: #778087; margin-bottom: 8px;">后端技术</h4>
                        <ul style="margin-left: 15px; color: #666; font-size: 13px;">
                            <li>Node.js + Express.js</li>
                            <li>SQLite 数据库</li>
                            <li>RESTful API</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: #778087; margin-bottom: 8px;">前端技术</h4>
                        <ul style="margin-left: 15px; color: #666; font-size: 13px;">
                            <li>原生 HTML/CSS/JS</li>
                            <li>响应式设计</li>
                            <li>现代化界面</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: #778087; margin-bottom: 8px;">核心特点</h4>
                        <ul style="margin-left: 15px; color: #666; font-size: 13px;">
                            <li>极简设计理念</li>
                            <li>快速部署</li>
                            <li>易于扩展</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body" style="text-align: center;">
                <h3 style="margin-bottom: 10px;">开始您的论坛体验</h3>
                <p style="margin-bottom: 15px; color: #666;">加入我们的社区，分享知识，交流想法</p>
                <div>
                    <a href="/list" class="btn btn-outline" style="margin-right: 10px;">查看所有帖子</a>
                    <a href="/post" class="btn btn-success">立即发帖</a>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2024 HaoWriter 论坛系统 | 基于 Express.js + SQLite 构建</p>
            <p style="margin-top: 8px;">
                <a href="https://github.com">GitHub</a> |
                <a href="/api/health">系统状态</a>
            </p>
        </div>
    </footer>

    <script src="/js/main.js"></script>
</body>
</html>
