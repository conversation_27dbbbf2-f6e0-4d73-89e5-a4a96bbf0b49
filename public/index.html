<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HaoWriter 极简论坛</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🌟 HaoWriter 论坛</h1>
            <p>一个极简而优雅的交流平台</p>
        </div>
    </div>

    <div class="container">
        <div class="nav">
            <a href="/" class="active">首页</a>
            <a href="/list">帖子列表</a>
            <a href="/post">发表帖子</a>
        </div>

        <div class="card">
            <h2>欢迎来到 HaoWriter 论坛！</h2>
            <p>这是一个基于 Express.js 和 SQLite 构建的极简论坛系统。在这里，您可以：</p>
            
            <div style="margin: 20px 0;">
                <h3>✨ 主要功能</h3>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    <li><strong>📝 发表帖子</strong> - 分享您的想法和见解</li>
                    <li><strong>💬 参与讨论</strong> - 回复感兴趣的帖子</li>
                    <li><strong>📚 浏览内容</strong> - 查看所有用户发布的帖子</li>
                    <li><strong>🔍 简洁界面</strong> - 专注于内容，无干扰体验</li>
                </ul>
            </div>

            <div style="margin: 20px 0;">
                <h3>🚀 快速开始</h3>
                <p>选择下面的操作开始您的论坛之旅：</p>
                
                <div style="margin-top: 20px; text-align: center;">
                    <a href="/list" class="btn btn-primary" style="margin: 0 10px;">浏览帖子</a>
                    <a href="/post" class="btn btn-success" style="margin: 0 10px;">发表帖子</a>
                </div>
            </div>

            <div style="margin: 20px 0;">
                <h3>💡 使用提示</h3>
                <ul style="margin-left: 20px; line-height: 1.8; color: #666;">
                    <li>点击帖子标题可以查看详细内容和回复</li>
                    <li>发帖时请填写真实的作者姓名</li>
                    <li>回复时请保持友善和建设性的讨论</li>
                    <li>系统会自动记录发帖和回复的时间</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h3>🛠️ 技术特性</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 15px;">
                <div>
                    <h4 style="color: #3498db; margin-bottom: 10px;">后端技术</h4>
                    <ul style="margin-left: 15px; color: #666;">
                        <li>Node.js + Express.js</li>
                        <li>SQLite 数据库</li>
                        <li>RESTful API</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #27ae60; margin-bottom: 10px;">前端技术</h4>
                    <ul style="margin-left: 15px; color: #666;">
                        <li>原生 HTML/CSS/JS</li>
                        <li>响应式设计</li>
                        <li>现代化界面</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #e74c3c; margin-bottom: 10px;">核心特点</h4>
                    <ul style="margin-left: 15px; color: #666;">
                        <li>极简设计理念</li>
                        <li>快速部署</li>
                        <li>易于扩展</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card" style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h3 style="color: white; margin-bottom: 15px;">🎯 开始您的论坛体验</h3>
            <p style="margin-bottom: 20px; opacity: 0.9;">加入我们的社区，分享知识，交流想法</p>
            <div>
                <a href="/list" class="btn" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); margin: 0 10px;">查看所有帖子</a>
                <a href="/post" class="btn" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); margin: 0 10px;">立即发帖</a>
            </div>
        </div>
    </div>

    <footer style="text-align: center; padding: 40px 20px; color: #7f8c8d; border-top: 1px solid #eee; margin-top: 40px;">
        <p>&copy; 2024 HaoWriter 论坛系统 | 基于 Express.js + SQLite 构建</p>
        <p style="margin-top: 10px; font-size: 0.9em;">
            <a href="https://github.com" style="color: #3498db; text-decoration: none;">GitHub</a> | 
            <a href="/api/health" style="color: #3498db; text-decoration: none;">系统状态</a>
        </p>
    </footer>

    <script src="/js/main.js"></script>
</body>
</html>
