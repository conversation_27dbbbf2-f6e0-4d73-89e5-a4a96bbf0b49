// 工具函数
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showMessage(message, type = 'success') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    const container = document.querySelector('.container');
    container.insertBefore(messageDiv, container.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

function showLoading(element) {
    element.innerHTML = '<div class="loading">加载中...</div>';
}

function showError(element, message) {
    element.innerHTML = `<div class="message error">${message}</div>`;
}

// API调用函数
async function apiCall(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || '请求失败');
        }
        
        return data;
    } catch (error) {
        console.error('API调用失败:', error);
        throw error;
    }
}

// 帖子列表相关函数
async function loadPosts() {
    const postsContainer = document.getElementById('posts-container');
    if (!postsContainer) return;
    
    showLoading(postsContainer);
    
    try {
        const response = await apiCall('/api/posts');
        const posts = response.data;
        
        if (posts.length === 0) {
            postsContainer.innerHTML = `
                <div class="empty-state">
                    <h3>暂无帖子</h3>
                    <p>成为第一个发帖的人吧！</p>
                    <a href="/post" class="btn btn-primary">立即发帖</a>
                </div>
            `;
            return;
        }
        
        postsContainer.innerHTML = posts.map(post => `
            <div class="post-item">
                <a href="/detail/${post.id}" class="post-title">${post.title}</a>
                <div class="post-meta">
                    作者：${post.author} | 发布时间：${formatDate(post.created_at)}
                </div>
                <div class="post-content-preview">
                    ${post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content}
                </div>
                <div class="post-stats">
                    回复数：${post.reply_count || 0}
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        showError(postsContainer, '加载帖子列表失败：' + error.message);
    }
}

// 发帖相关函数
async function submitPost(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const postData = {
        title: formData.get('title').trim(),
        content: formData.get('content').trim(),
        author: formData.get('author').trim()
    };
    
    // 前端验证
    if (!postData.title) {
        showMessage('请输入帖子标题', 'error');
        return;
    }
    
    if (!postData.content) {
        showMessage('请输入帖子内容', 'error');
        return;
    }
    
    if (!postData.author) {
        showMessage('请输入作者姓名', 'error');
        return;
    }
    
    // 禁用提交按钮
    submitBtn.disabled = true;
    submitBtn.textContent = '发布中...';
    
    try {
        await apiCall('/api/posts', {
            method: 'POST',
            body: JSON.stringify(postData)
        });
        
        showMessage('帖子发布成功！');
        form.reset();
        
        // 2秒后跳转到帖子列表
        setTimeout(() => {
            window.location.href = '/list';
        }, 2000);
        
    } catch (error) {
        showMessage('发布失败：' + error.message, 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = '发布帖子';
    }
}

// 帖子详情相关函数
async function loadPostDetail() {
    const postId = getPostIdFromUrl();
    if (!postId) {
        showMessage('无效的帖子ID', 'error');
        return;
    }
    
    const postContainer = document.getElementById('post-container');
    const repliesContainer = document.getElementById('replies-container');
    
    if (!postContainer) return;
    
    showLoading(postContainer);
    
    try {
        // 加载帖子详情
        const postResponse = await apiCall(`/api/posts/${postId}`);
        const post = postResponse.data;
        
        postContainer.innerHTML = `
            <div class="post-detail">
                <h1>${post.title}</h1>
                <div class="post-meta">
                    作者：${post.author} | 发布时间：${formatDate(post.created_at)}
                </div>
                <div class="post-content">${post.content}</div>
            </div>
        `;
        
        // 加载回复
        if (repliesContainer) {
            await loadReplies(postId);
        }
        
    } catch (error) {
        showError(postContainer, '加载帖子详情失败：' + error.message);
    }
}

async function loadReplies(postId) {
    const repliesContainer = document.getElementById('replies-container');
    if (!repliesContainer) return;
    
    try {
        const response = await apiCall(`/api/posts/${postId}/replies`);
        const replies = response.data;
        
        if (replies.length === 0) {
            repliesContainer.innerHTML = `
                <div class="empty-state">
                    <h3>暂无回复</h3>
                    <p>成为第一个回复的人吧！</p>
                </div>
            `;
            return;
        }
        
        repliesContainer.innerHTML = `
            <h3>回复 (${replies.length})</h3>
            ${replies.map(reply => `
                <div class="reply-item">
                    <div class="reply-meta">
                        ${reply.author} | ${formatDate(reply.created_at)}
                    </div>
                    <div class="reply-content">${reply.content}</div>
                </div>
            `).join('')}
        `;
        
    } catch (error) {
        showError(repliesContainer, '加载回复失败：' + error.message);
    }
}

async function submitReply(event) {
    event.preventDefault();
    
    const postId = getPostIdFromUrl();
    if (!postId) {
        showMessage('无效的帖子ID', 'error');
        return;
    }
    
    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const replyData = {
        content: formData.get('content').trim(),
        author: formData.get('author').trim()
    };
    
    // 前端验证
    if (!replyData.content) {
        showMessage('请输入回复内容', 'error');
        return;
    }
    
    if (!replyData.author) {
        showMessage('请输入作者姓名', 'error');
        return;
    }
    
    // 禁用提交按钮
    submitBtn.disabled = true;
    submitBtn.textContent = '回复中...';
    
    try {
        await apiCall(`/api/posts/${postId}/replies`, {
            method: 'POST',
            body: JSON.stringify(replyData)
        });
        
        showMessage('回复成功！');
        form.reset();
        
        // 重新加载回复列表
        await loadReplies(postId);
        
    } catch (error) {
        showMessage('回复失败：' + error.message, 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = '发表回复';
    }
}

// 工具函数
function getPostIdFromUrl() {
    const path = window.location.pathname;
    const matches = path.match(/\/detail\/(\d+)/);
    return matches ? matches[1] : null;
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 根据页面类型执行相应的初始化函数
    const path = window.location.pathname;
    
    if (path === '/list') {
        loadPosts();
    } else if (path.startsWith('/detail/')) {
        loadPostDetail();
    }
    
    // 绑定表单提交事件
    const postForm = document.getElementById('post-form');
    if (postForm) {
        postForm.addEventListener('submit', submitPost);
    }
    
    const replyForm = document.getElementById('reply-form');
    if (replyForm) {
        replyForm.addEventListener('submit', submitReply);
    }
});
