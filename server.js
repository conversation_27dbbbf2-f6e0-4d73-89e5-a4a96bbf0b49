const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const database = require('./database/database');

// 导入路由
const postsRouter = require('./routes/posts');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));
app.use('/views', express.static(path.join(__dirname, 'views')));

// API路由
app.use('/api/posts', postsRouter);

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 帖子列表页面
app.get('/list', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'list.html'));
});

// 发帖页面
app.get('/post', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'post.html'));
});

// 帖子详情页面
app.get('/detail/:id', (req, res) => {
    res.sendFile(path.join(__dirname, 'views', 'detail.html'));
});

// 健康检查接口
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'HaoWriter论坛系统运行正常',
        timestamp: new Date().toISOString()
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '页面未找到'
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? err.message : '服务器错误'
    });
});

// 启动服务器
async function startServer() {
    try {
        // 连接数据库
        await database.connect();
        console.log('数据库连接成功');

        // 启动HTTP服务器
        app.listen(PORT, () => {
            console.log(`🚀 HaoWriter论坛系统启动成功！`);
            console.log(`📍 服务器地址: http://localhost:${PORT}`);
            console.log(`📚 API文档: http://localhost:${PORT}/api/health`);
            console.log(`🎯 主页面: http://localhost:${PORT}`);
            console.log(`📝 帖子列表: http://localhost:${PORT}/list`);
            console.log(`✏️  发帖页面: http://localhost:${PORT}/post`);
        });
    } catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
}

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('\n正在关闭服务器...');
    try {
        await database.close();
        console.log('数据库连接已关闭');
        process.exit(0);
    } catch (error) {
        console.error('关闭服务器时出错:', error);
        process.exit(1);
    }
});

process.on('SIGTERM', async () => {
    console.log('\n收到终止信号，正在关闭服务器...');
    try {
        await database.close();
        console.log('数据库连接已关闭');
        process.exit(0);
    } catch (error) {
        console.error('关闭服务器时出错:', error);
        process.exit(1);
    }
});

// 启动服务器
startServer();
