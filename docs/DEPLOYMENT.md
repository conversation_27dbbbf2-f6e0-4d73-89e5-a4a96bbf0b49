# HaoWriter 论坛系统部署指南

## 🌐 部署选项

### 1. 本地开发环境部署

#### 系统要求
- Node.js 14.0+
- npm 6.0+
- 至少 100MB 可用磁盘空间

#### 部署步骤
```bash
# 1. 下载项目
git clone <repository-url>
cd HaoWriter

# 2. 安装依赖
npm install

# 3. 初始化数据库
npm run init-db

# 4. 启动服务
npm start
```

访问地址: http://localhost:3000

### 2. 生产环境部署

#### 使用 PM2 (推荐)
```bash
# 1. 全局安装 PM2
npm install -g pm2

# 2. 创建 PM2 配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'hao-forum',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# 3. 启动应用
pm2 start ecosystem.config.js

# 4. 设置开机自启
pm2 startup
pm2 save
```

#### 使用 Docker
```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

RUN npm run init-db

EXPOSE 3000

CMD ["npm", "start"]
```

```bash
# 构建和运行
docker build -t hao-forum .
docker run -d -p 3000:3000 --name hao-forum-container hao-forum
```

#### 使用 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  hao-forum:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./database:/app/database
    restart: unless-stopped
    environment:
      - NODE_ENV=production
```

```bash
# 启动服务
docker-compose up -d
```

### 3. 云平台部署

#### Heroku 部署
```bash
# 1. 安装 Heroku CLI
# 2. 登录 Heroku
heroku login

# 3. 创建应用
heroku create your-forum-name

# 4. 设置环境变量
heroku config:set NODE_ENV=production

# 5. 部署
git push heroku main
```

#### Vercel 部署
```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "server.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/server.js"
    }
  ]
}
```

#### Railway 部署
1. 连接 GitHub 仓库
2. 选择项目
3. 自动部署

## 🔧 环境配置

### 环境变量
```bash
# .env 文件示例
NODE_ENV=production
PORT=3000
DB_PATH=./database/forum.db
```

### 生产环境优化
```javascript
// server.js 中添加
if (process.env.NODE_ENV === 'production') {
    // 启用压缩
    app.use(require('compression')());
    
    // 设置安全头
    app.use(require('helmet')());
    
    // 限制请求频率
    const rateLimit = require('express-rate-limit');
    app.use(rateLimit({
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 100 // 限制每个IP 100次请求
    }));
}
```

## 🔒 安全配置

### 1. 反向代理 (Nginx)
```nginx
# /etc/nginx/sites-available/hao-forum
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 2. SSL 证书 (Let's Encrypt)
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 📊 监控和日志

### 1. 日志管理
```javascript
// 添加到 server.js
const winston = require('winston');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
    ),
    transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' })
    ]
});

if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: winston.format.simple()
    }));
}
```

### 2. 性能监控
```bash
# 使用 PM2 监控
pm2 monit

# 查看日志
pm2 logs hao-forum

# 重启应用
pm2 restart hao-forum
```

## 🔄 备份和恢复

### 数据库备份
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"
DB_FILE="/path/to/database/forum.db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $DB_FILE $BACKUP_DIR/forum_backup_$DATE.db

# 保留最近30天的备份
find $BACKUP_DIR -name "forum_backup_*.db" -mtime +30 -delete

echo "备份完成: forum_backup_$DATE.db"
```

### 自动备份 (Cron)
```bash
# 编辑 crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

### 数据恢复
```bash
# 停止服务
pm2 stop hao-forum

# 恢复数据库
cp /path/to/backups/forum_backup_YYYYMMDD_HHMMSS.db /path/to/database/forum.db

# 重启服务
pm2 start hao-forum
```

## 🚀 性能优化

### 1. 数据库优化
```sql
-- 为常用查询添加索引
CREATE INDEX idx_posts_created_at ON posts(created_at);
CREATE INDEX idx_replies_post_id ON replies(post_id);
CREATE INDEX idx_replies_created_at ON replies(created_at);
```

### 2. 缓存策略
```javascript
// 添加内存缓存
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 600 }); // 10分钟缓存

// 在路由中使用
app.get('/api/posts', async (req, res) => {
    const cacheKey = 'posts_list';
    let posts = cache.get(cacheKey);
    
    if (!posts) {
        posts = await database.getPosts();
        cache.set(cacheKey, posts);
    }
    
    res.json({ success: true, data: posts });
});
```

### 3. 静态资源优化
```javascript
// 启用静态资源缓存
app.use(express.static('public', {
    maxAge: '1d', // 1天缓存
    etag: true
}));
```

## 📋 部署检查清单

- [ ] Node.js 和 npm 版本检查
- [ ] 依赖包安装完成
- [ ] 数据库初始化成功
- [ ] 环境变量配置正确
- [ ] 端口配置和防火墙设置
- [ ] SSL 证书配置（生产环境）
- [ ] 反向代理配置（如使用）
- [ ] 日志和监控设置
- [ ] 备份策略实施
- [ ] 性能测试通过
- [ ] 安全扫描完成

## 🆘 故障排除

### 常见问题
1. **端口占用**: 使用 `lsof -i :3000` 检查端口使用情况
2. **权限问题**: 确保应用有读写数据库文件的权限
3. **内存不足**: 监控内存使用，考虑增加服务器配置
4. **数据库锁定**: 重启应用释放数据库连接

### 日志分析
```bash
# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/combined.log

# 使用 PM2 查看日志
pm2 logs hao-forum --lines 100
```
