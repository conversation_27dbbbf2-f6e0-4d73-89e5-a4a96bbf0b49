# HaoWriter 论坛系统使用指南

## 🚀 快速开始

### 1. 系统要求
- Node.js 14.0 或更高版本
- npm 6.0 或更高版本
- 支持现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 安装和启动

```bash
# 1. 克隆或下载项目到本地
cd HaoWriter

# 2. 安装依赖
npm install

# 3. 初始化数据库
npm run init-db

# 4. 启动服务器
npm start
```

服务器启动后，访问 http://localhost:3000 即可使用论坛系统。

## 📱 功能使用说明

### 主页面
- 访问 http://localhost:3000
- 查看系统介绍和功能说明
- 快速导航到其他页面

### 浏览帖子
1. 点击导航栏的"帖子列表"或访问 http://localhost:3000/list
2. 查看所有帖子的列表，包括：
   - 帖子标题
   - 作者和发布时间
   - 内容预览（前100字符）
   - 回复数量统计
3. 点击帖子标题查看详细内容

### 发表帖子
1. 点击导航栏的"发表帖子"或访问 http://localhost:3000/post
2. 填写表单：
   - **帖子标题**：简洁明了的标题（必填，最多100字符）
   - **作者姓名**：您的姓名或昵称（必填，最多50字符）
   - **帖子内容**：详细的内容描述（必填）
3. 点击"发布帖子"按钮
4. 发布成功后会自动跳转到帖子列表页

### 查看帖子详情和回复
1. 在帖子列表页点击任意帖子标题
2. 查看完整的帖子内容和所有回复
3. 在页面底部的回复表单中发表回复：
   - **您的姓名**：回复者姓名（必填）
   - **回复内容**：回复的具体内容（必填）
4. 点击"发表回复"按钮提交回复

## 🔧 API 接口说明

### 基础信息
- 基础URL: http://localhost:3000/api
- 数据格式: JSON
- 字符编码: UTF-8

### 接口列表

#### 1. 健康检查
```
GET /api/health
```
**响应示例:**
```json
{
  "success": true,
  "message": "HaoWriter论坛系统运行正常",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 2. 获取帖子列表
```
GET /api/posts
```
**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "帖子标题",
      "content": "帖子内容",
      "author": "作者姓名",
      "created_at": "2024-01-01 12:00:00",
      "reply_count": 5
    }
  ],
  "message": "获取帖子列表成功"
}
```

#### 3. 获取帖子详情
```
GET /api/posts/:id
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "帖子标题",
    "content": "帖子内容",
    "author": "作者姓名",
    "created_at": "2024-01-01 12:00:00"
  },
  "message": "获取帖子详情成功"
}
```

#### 4. 创建新帖子
```
POST /api/posts
Content-Type: application/json

{
  "title": "帖子标题",
  "content": "帖子内容",
  "author": "作者姓名"
}
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "title": "帖子标题",
    "content": "帖子内容",
    "author": "作者姓名"
  },
  "message": "帖子创建成功"
}
```

#### 5. 获取帖子回复
```
GET /api/posts/:id/replies
```
**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "post_id": 1,
      "content": "回复内容",
      "author": "回复者姓名",
      "created_at": "2024-01-01 12:30:00"
    }
  ],
  "message": "获取回复列表成功"
}
```

#### 6. 创建回复
```
POST /api/posts/:id/replies
Content-Type: application/json

{
  "content": "回复内容",
  "author": "回复者姓名"
}
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "post_id": 1,
    "content": "回复内容",
    "author": "回复者姓名"
  },
  "message": "回复创建成功"
}
```

## 🧪 测试

### 运行自动化测试
```bash
node test/api-test.js
```

### 手动测试步骤
1. **启动服务器**: `npm start`
2. **访问主页**: http://localhost:3000
3. **测试发帖功能**: 
   - 访问发帖页面
   - 填写并提交表单
   - 验证跳转和成功提示
4. **测试浏览功能**:
   - 访问帖子列表页
   - 点击帖子标题查看详情
5. **测试回复功能**:
   - 在帖子详情页发表回复
   - 验证回复显示

## 🔍 故障排除

### 常见问题

**Q: 服务器启动失败**
A: 检查端口3000是否被占用，或修改server.js中的端口配置

**Q: 数据库初始化失败**
A: 确保有写入权限，删除database/forum.db文件后重新运行`npm run init-db`

**Q: 页面无法访问**
A: 确认服务器已启动，检查防火墙设置

**Q: API返回错误**
A: 查看服务器控制台日志，检查请求格式是否正确

### 开发模式
```bash
# 使用nodemon自动重启服务器
npm run dev
```

### 数据库管理
```bash
# 重新初始化数据库（会清空所有数据）
npm run init-db
```

## 📝 注意事项

1. **数据持久化**: 数据存储在SQLite数据库文件中，位于`database/forum.db`
2. **字符限制**: 标题最多100字符，作者姓名最多50字符
3. **时间格式**: 系统使用本地时间格式显示创建时间
4. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验
5. **数据备份**: 定期备份`database/forum.db`文件以防数据丢失

## 🚀 扩展建议

如需扩展功能，可以考虑添加：
- 用户认证和权限管理
- 帖子分类和标签
- 搜索功能
- 分页功能
- 富文本编辑器
- 文件上传功能
- 邮件通知
- 管理后台
