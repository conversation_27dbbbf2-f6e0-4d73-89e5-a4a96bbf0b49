<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帖子详情 - HaoWriter 论坛</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>帖子详情</h1>
            <p>查看完整内容和参与讨论</p>
        </div>
    </div>

    <div class="nav">
        <div class="container">
            <a href="/">首页</a>
            <a href="/list">帖子列表</a>
            <a href="/post">发表帖子</a>
        </div>
    </div>

    <div class="container">
        <!-- 帖子内容区域 -->
        <div class="card">
            <div id="post-container">
                <div class="loading">正在加载帖子详情...</div>
            </div>
        </div>

        <!-- 回复列表区域 -->
        <div class="replies-section">
            <div id="replies-container">
                <div class="loading">正在加载回复...</div>
            </div>
        </div>

        <!-- 回复表单区域 -->
        <div class="reply-form">
            <h4>发表回复</h4>
            <div class="reply-form-body">
                <p style="color: #666; margin-bottom: 15px;">参与讨论，分享您的观点</p>

                <form id="reply-form">
                    <div class="form-group">
                        <label for="reply-author">您的姓名 *</label>
                        <input
                            type="text"
                            id="reply-author"
                            name="author"
                            placeholder="请输入您的姓名..."
                            required
                            maxlength="50"
                        >
                    </div>

                    <div class="form-group">
                        <label for="reply-content">回复内容 *</label>
                        <textarea
                            id="reply-content"
                            name="content"
                            placeholder="请输入您的回复内容...&#10;&#10;您可以：&#10;- 回答问题或提供帮助&#10;- 分享相关经验&#10;- 表达不同观点&#10;- 补充有用信息"
                            required
                            rows="6"
                        ></textarea>
                    </div>

                    <div style="text-align: right;">
                        <button type="submit" class="btn btn-primary">
                            发表回复
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作提示 -->
        <div class="card">
            <div class="card-header">
                参与讨论
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div>
                        <h4 style="color: #778087; margin-bottom: 8px;">回复建议</h4>
                        <ul style="margin-left: 15px; color: #666; line-height: 1.6; font-size: 13px;">
                            <li>仔细阅读原帖内容</li>
                            <li>提供有价值的信息</li>
                            <li>保持友善和尊重</li>
                            <li>避免重复他人观点</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: #778087; margin-bottom: 8px;">讨论礼仪</h4>
                        <ul style="margin-left: 15px; color: #666; line-height: 1.6; font-size: 13px;">
                            <li>使用礼貌的语言</li>
                            <li>尊重不同观点</li>
                            <li>基于事实进行讨论</li>
                            <li>避免人身攻击</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div style="text-align: center; margin: 20px 0;">
            <a href="/list" class="btn btn-outline" style="margin-right: 10px;">
                返回列表
            </a>
            <a href="/post" class="btn btn-success">
                发表新帖
            </a>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2024 HaoWriter 论坛系统</p>
        </div>
    </footer>

    <script src="/js/main.js"></script>
</body>
</html>
