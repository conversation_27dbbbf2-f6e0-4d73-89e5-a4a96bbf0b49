# HaoWriter 极简论坛系统

一个基于 Express.js 和 SQLite 的极简论坛系统，支持发帖、回帖和浏览功能。

## 功能特性

- ✅ 发布帖子
- ✅ 浏览帖子列表
- ✅ 查看帖子详情
- ✅ 回复帖子
- ✅ 简洁的用户界面
- ✅ RESTful API 接口

## 技术栈

- **后端**: Node.js + Express.js
- **数据库**: SQLite
- **前端**: HTML + CSS + JavaScript
- **其他**: CORS, Body-parser

## 项目结构

```
HaoWriter/
├── package.json              # 项目配置文件
├── README.md                 # 项目说明文档
├── server.js                 # 主服务器文件
├── database/
│   ├── init.js              # 数据库初始化脚本
│   ├── init.sql             # SQL初始化语句
│   └── database.js          # 数据库连接模块
├── routes/
│   ├── posts.js             # 帖子相关路由
│   └── replies.js           # 回复相关路由
├── public/
│   ├── css/
│   │   └── style.css        # 样式文件
│   ├── js/
│   │   └── main.js          # 前端JavaScript
│   └── index.html           # 主页面
└── views/
    ├── list.html            # 帖子列表页
    ├── post.html            # 发帖页
    └── detail.html          # 帖子详情页
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 初始化数据库

```bash
npm run init-db
```

### 3. 启动服务器

```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

### 4. 访问应用

打开浏览器访问: http://localhost:3000

## API 接口

### 帖子相关

- `GET /api/posts` - 获取帖子列表
- `POST /api/posts` - 创建新帖子
- `GET /api/posts/:id` - 获取帖子详情

### 回复相关

- `GET /api/posts/:id/replies` - 获取帖子的所有回复
- `POST /api/posts/:id/replies` - 创建回复

## 数据库结构

### posts 表
- `id` - 主键
- `title` - 帖子标题
- `content` - 帖子内容
- `author` - 作者
- `created_at` - 创建时间

### replies 表
- `id` - 主键
- `post_id` - 关联帖子ID
- `content` - 回复内容
- `author` - 回复者
- `created_at` - 回复时间

## 开发说明

本项目采用简洁的架构设计，适合学习和快速开发。如需扩展功能，可以考虑添加：

- 用户认证系统
- 帖子分类功能
- 搜索功能
- 分页功能
- 富文本编辑器

## 许可证

MIT License
