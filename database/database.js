const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const DB_PATH = path.join(__dirname, 'forum.db');

class Database {
    constructor() {
        this.db = null;
    }

    // 连接数据库
    connect() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(DB_PATH, (err) => {
                if (err) {
                    console.error('数据库连接失败:', err.message);
                    reject(err);
                } else {
                    console.log('数据库连接成功');
                    resolve();
                }
            });
        });
    }

    // 关闭数据库连接
    close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('数据库关闭失败:', err.message);
                        reject(err);
                    } else {
                        console.log('数据库连接已关闭');
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }

    // 执行查询（返回多行结果）
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('查询执行失败:', err.message);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // 执行查询（返回单行结果）
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('查询执行失败:', err.message);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // 执行插入、更新、删除操作
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('操作执行失败:', err.message);
                    reject(err);
                } else {
                    resolve({
                        id: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }

    // 获取帖子列表
    async getPosts() {
        const sql = `
            SELECT p.*, 
                   COUNT(r.id) as reply_count
            FROM posts p
            LEFT JOIN replies r ON p.id = r.post_id
            GROUP BY p.id
            ORDER BY p.created_at DESC
        `;
        return await this.all(sql);
    }

    // 根据ID获取帖子详情
    async getPostById(id) {
        const sql = 'SELECT * FROM posts WHERE id = ?';
        return await this.get(sql, [id]);
    }

    // 创建新帖子
    async createPost(title, content, author) {
        const sql = 'INSERT INTO posts (title, content, author) VALUES (?, ?, ?)';
        return await this.run(sql, [title, content, author]);
    }

    // 获取帖子的所有回复
    async getRepliesByPostId(postId) {
        const sql = 'SELECT * FROM replies WHERE post_id = ? ORDER BY created_at ASC';
        return await this.all(sql, [postId]);
    }

    // 创建回复
    async createReply(postId, content, author) {
        const sql = 'INSERT INTO replies (post_id, content, author) VALUES (?, ?, ?)';
        return await this.run(sql, [postId, content, author]);
    }
}

// 创建数据库实例
const database = new Database();

module.exports = database;
