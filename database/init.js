const fs = require('fs');
const path = require('path');
const database = require('./database');

async function initializeDatabase() {
    try {
        console.log('开始初始化数据库...');
        
        // 连接数据库
        await database.connect();
        
        // 读取SQL初始化脚本
        const sqlPath = path.join(__dirname, 'init.sql');
        const sqlContent = fs.readFileSync(sqlPath, 'utf8');
        
        // 分割SQL语句（按分号分割）
        const sqlStatements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);
        
        // 执行每个SQL语句
        for (const sql of sqlStatements) {
            try {
                await database.run(sql);
                console.log('执行SQL语句成功:', sql.substring(0, 50) + '...');
            } catch (err) {
                console.error('执行SQL语句失败:', sql.substring(0, 50) + '...');
                console.error('错误信息:', err.message);
            }
        }
        
        console.log('数据库初始化完成！');
        
        // 验证数据
        const posts = await database.getPosts();
        console.log(`成功创建 ${posts.length} 个示例帖子`);
        
        const replies = await database.all('SELECT COUNT(*) as count FROM replies');
        console.log(`成功创建 ${replies[0].count} 个示例回复`);
        
    } catch (error) {
        console.error('数据库初始化失败:', error.message);
        process.exit(1);
    } finally {
        // 关闭数据库连接
        await database.close();
        console.log('数据库初始化脚本执行完毕');
    }
}

// 如果直接运行此脚本，则执行初始化
if (require.main === module) {
    initializeDatabase();
}

module.exports = initializeDatabase;
