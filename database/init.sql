-- 创建帖子表
CREATE TABLE IF NOT EXISTS posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    author TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建回复表
CREATE TABLE IF NOT EXIS<PERSON> replies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    post_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    author TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES posts (id) ON DELETE CASCADE
);

-- 插入示例数据
INSERT INTO posts (title, content, author) VALUES 
('欢迎来到HaoWriter论坛', '这是第一个帖子，欢迎大家在这里分享和讨论！', '管理员'),
('如何使用这个论坛？', '点击"发帖"按钮可以创建新帖子，点击帖子标题可以查看详情和回复。', '管理员'),
('技术讨论区', '欢迎大家在这里讨论各种技术问题，互相学习进步！', '技术小白');

INSERT INTO replies (post_id, content, author) VALUES 
(1, '感谢管理员！这个论坛界面很简洁，我很喜欢。', '用户A'),
(1, '期待更多功能的加入！', '用户B'),
(2, '界面确实很直观，操作简单。', '用户C'),
(3, '我想讨论一下JavaScript的异步编程，有人感兴趣吗？', '前端开发者');
