{"name": "hao-forum", "version": "1.0.0", "description": "一个极简的论坛系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node database/init.js", "test": "node test/api-test.js", "test:api": "node test/api-test.js"}, "keywords": ["forum", "express", "sqlite", "nodejs"], "author": "HaoWriter", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "body-parser": "^1.20.2", "cors": "^2.8.5", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1"}}