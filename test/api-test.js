const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const TEST_TIMEOUT = 5000;

// 测试结果统计
let testResults = {
    total: 0,
    passed: 0,
    failed: 0
};

// HTTP请求工具函数
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: jsonBody
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// 测试断言函数
function assert(condition, message) {
    testResults.total++;
    if (condition) {
        testResults.passed++;
        console.log(`✅ ${message}`);
    } else {
        testResults.failed++;
        console.log(`❌ ${message}`);
    }
}

// 测试用例
async function runTests() {
    console.log('🚀 开始API测试...\n');

    try {
        // 测试1: 健康检查
        console.log('📋 测试1: 健康检查API');
        const healthResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/health',
            method: 'GET'
        });
        
        assert(healthResponse.statusCode === 200, '健康检查API返回状态码200');
        assert(healthResponse.body.success === true, '健康检查API返回success为true');
        assert(typeof healthResponse.body.timestamp === 'string', '健康检查API返回时间戳');

        // 测试2: 获取帖子列表
        console.log('\n📋 测试2: 获取帖子列表API');
        const postsResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/posts',
            method: 'GET'
        });
        
        assert(postsResponse.statusCode === 200, '获取帖子列表API返回状态码200');
        assert(postsResponse.body.success === true, '获取帖子列表API返回success为true');
        assert(Array.isArray(postsResponse.body.data), '获取帖子列表API返回数据是数组');
        assert(postsResponse.body.data.length > 0, '帖子列表不为空');

        // 测试3: 创建新帖子
        console.log('\n📋 测试3: 创建新帖子API');
        const newPost = {
            title: '自动化测试帖子',
            content: '这是通过自动化测试创建的帖子内容',
            author: '测试机器人'
        };
        
        const createPostResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/posts',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }, newPost);
        
        assert(createPostResponse.statusCode === 201, '创建帖子API返回状态码201');
        assert(createPostResponse.body.success === true, '创建帖子API返回success为true');
        assert(typeof createPostResponse.body.data.id === 'number', '创建帖子API返回帖子ID');
        
        const createdPostId = createPostResponse.body.data.id;

        // 测试4: 获取帖子详情
        console.log('\n📋 测试4: 获取帖子详情API');
        const postDetailResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: `/api/posts/${createdPostId}`,
            method: 'GET'
        });
        
        assert(postDetailResponse.statusCode === 200, '获取帖子详情API返回状态码200');
        assert(postDetailResponse.body.success === true, '获取帖子详情API返回success为true');
        assert(postDetailResponse.body.data.title === newPost.title, '帖子标题匹配');
        assert(postDetailResponse.body.data.content === newPost.content, '帖子内容匹配');
        assert(postDetailResponse.body.data.author === newPost.author, '帖子作者匹配');

        // 测试5: 获取帖子回复列表
        console.log('\n📋 测试5: 获取帖子回复列表API');
        const repliesResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: `/api/posts/${createdPostId}/replies`,
            method: 'GET'
        });
        
        assert(repliesResponse.statusCode === 200, '获取回复列表API返回状态码200');
        assert(repliesResponse.body.success === true, '获取回复列表API返回success为true');
        assert(Array.isArray(repliesResponse.body.data), '回复列表是数组');

        // 测试6: 创建回复
        console.log('\n📋 测试6: 创建回复API');
        const newReply = {
            content: '这是通过自动化测试创建的回复',
            author: '测试回复机器人'
        };
        
        const createReplyResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: `/api/posts/${createdPostId}/replies`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }, newReply);
        
        assert(createReplyResponse.statusCode === 201, '创建回复API返回状态码201');
        assert(createReplyResponse.body.success === true, '创建回复API返回success为true');
        assert(typeof createReplyResponse.body.data.id === 'number', '创建回复API返回回复ID');

        // 测试7: 验证回复已创建
        console.log('\n📋 测试7: 验证回复已创建');
        const updatedRepliesResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: `/api/posts/${createdPostId}/replies`,
            method: 'GET'
        });
        
        assert(updatedRepliesResponse.body.data.length === 1, '回复列表包含新创建的回复');
        assert(updatedRepliesResponse.body.data[0].content === newReply.content, '回复内容匹配');
        assert(updatedRepliesResponse.body.data[0].author === newReply.author, '回复作者匹配');

        // 测试8: 错误处理 - 获取不存在的帖子
        console.log('\n📋 测试8: 错误处理 - 获取不存在的帖子');
        const notFoundResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/posts/99999',
            method: 'GET'
        });
        
        assert(notFoundResponse.statusCode === 404, '获取不存在帖子返回404状态码');
        assert(notFoundResponse.body.success === false, '获取不存在帖子返回success为false');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        testResults.failed++;
    }

    // 输出测试结果
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果统计:');
    console.log(`总测试数: ${testResults.total}`);
    console.log(`通过: ${testResults.passed} ✅`);
    console.log(`失败: ${testResults.failed} ❌`);
    console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 所有测试通过！系统运行正常。');
    } else {
        console.log('\n⚠️  部分测试失败，请检查系统状态。');
    }
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests };
